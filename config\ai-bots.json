[{"id": "1755245448038", "name": "1-3 day holding", "enabled": false, "aiProvider": "gpt", "webhookUrl": "", "autoPlaceOrder": false, "scheduleCheck": false, "checkInterval": 60, "symbol": "XAUUSD", "timeframes": ["H1", "H4"], "barback": 50, "additionalPrompt": "Look for swing trading opportunities with 1-2 day waiting for order and 1-3 day holding periods. Consider 4H and 1H timeframe confluence. \nI bias Buy Limit on XAUUSD, but if the reason for Sell is enough then you can give Sell Signal, I like using the zone that have more than 2 of 1H & 4H Supply/Demand zone overlapped as entry point, to use top middle or bottom of the zone is up to the volatility at the times and use Support/Resistant and Trendline, EMA Retest (9) (20) (50) (100) (200), Stochastic (9) Smooth K (3) D (3) , RSI (14), ATR(14)  to make a decision to order. And pay attention on the economic news today on what direction will it go, based on past result.\nCurrent price: ", "createdAt": "2025-08-15T08:10:48.038Z"}, {"id": "1757318090504", "name": "<PERSON><PERSON><PERSON>", "enabled": false, "aiProvider": "gpt", "webhookUrl": "", "autoPlaceOrder": false, "scheduleCheck": false, "checkInterval": 60, "symbol": "XAUUSD", "timeframes": ["M15", "H1", "M5"], "barback": 50, "additionalPrompt": "Analyze for short-term scalping opportunities. Focus on 5-15 minute entries with tight stops and quick profit targets.\nConsider 15M and 1H timeframe confluence. \n\nI bias Buy Limit on XAUUSD, but if the reason for Sell is enough then you can give Sell Signal, I like using middle of the zone that have more than 3 of 1H & 4H Supply/Demand zone overlapped as entry point, and use Support/Resistant and Trendline, EMA Retest (9) (20) (50) (100) (200), Stochastic (9) Smooth K (3) D (3) , RSI (14), ATR(14)  to make a decision to order. And pay attention on the economic news today on what direction will it go, based on past result.\nCurrent price: ", "createdAt": "2025-09-08T07:54:50.504Z"}]