

https://teaching.suksapanpanit.com/extra/playground/test1.php

================================


setx GYP_MSVS_VERSION 2022 /M
npm install
npm run dev

================================

npm install discord.js
npm install zlib-sync bufferutil utf-8-validate


================================
============= Go Live =============
================================

npm run build
npm run start

;----- or run on background

npm install -g pm2
pm2 start npm --name "next-app" -- start
pm2 save
pm2 startup

================================
============= TEST =============
================================

🟡 PENDING 🟡


C.Ball
========================
Symbol : Xauusd
Status : Opened
Signal : Buy Limit
Price :  3332-3328
SL : 3323
TP1 : 3335
TP2 : 3344
TP3 : 3363
TP4: 3400
<@&1247398904576606270>  <@&1327952045176651807> <@&1247740639865606184>


Signal ID :  (ไม้รัน) 
C.Lew
========================
Symbol : XAUUSD.s
Status : PENDING
Signal : BUY
Price : 3309 - 3306 
SL : 3300
TP1 : 3320
TP2 : 3334
TP3 : 3345



Signal ID :  (ไม้เทส) 
C.BBB
========================
Symbol : XAUUSD.s
Status : PENDING
Signal : BUY
Price : 3209 - 3206 
SL : 3200
TP1 : 3320
TP2 : 3334
TP3 : 3345

================================
================================
================================

 

https://docs.google.com/forms/d/e/1FAIpQLSda4_GifbWv-MGp9j-2jdbtCYzUlDN-chjhprEMHUG4DkkH_g/viewform?usp=pp_url&entry.178506718={Symbol}&entry.645304246=Buy+Limit&entry.785671803={Entry}&entry.898028705={SL}&entry.1523790774={TP}&entry.381285031={Lot}&entry.1073635155={SignalID}&entry.1148525053={Comment}&entry.398055293={Reason}&entry.305299530={Risk}

