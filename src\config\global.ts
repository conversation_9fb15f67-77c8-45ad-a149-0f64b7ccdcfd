// config/global.ts

// USD to THB exchange rate (default - actual rate comes from app settings)
export const USD_TO_THB_RATE = 32.5;

// Currency mapping for symbols to Investing.com country IDs
export const SYMBOL_COUNTRY_MAP: { [key: string]: string } = {
  // Major Pairs
  "XAUUSD": "5", // Gold/USD - United States
  "GBPUSD": "5,4", // GBP/USD - United States, United Kingdom
  "EURUSD": "5,17", // EUR/USD - United States, European Union
  "USDJPY": "5,35", // USD/JPY - United States, Japan
  "USDCHF": "5,12", // USD/CHF - United States, Switzerland
  "AUDUSD": "5,25", // AUD/USD - United States, Australia
  "USDCAD": "5,6", // USD/CAD - United States, Canada
  "NZDUSD": "5,43", // NZD/USD - United States, New Zealand

  // Cross Pairs
  "EURGBP": "17,4", // EUR/GBP - European Union, United Kingdom
  "EURJPY": "17,35", // EUR/JPY - European Union, Japan
  "EURCHF": "17,12", // EUR/CHF - European Union, Switzerland
  "EURAUD": "17,25", // EUR/AUD - European Union, Australia
  "EURCAD": "17,6", // EUR/CAD - European Union, Canada
  "EURNZD": "17,43", // EUR/NZD - European Union, New Zealand
  "GBPJPY": "4,35", // GBP/JPY - United Kingdom, Japan
  "GBPCHF": "4,12", // GBP/CHF - United Kingdom, Switzerland
  "GBPAUD": "4,25", // GBP/AUD - United Kingdom, Australia
  "GBPCAD": "4,6", // GBP/CAD - United Kingdom, Canada
  "GBPNZD": "4,43", // GBP/NZD - United Kingdom, New Zealand
  "AUDJPY": "25,35", // AUD/JPY - Australia, Japan
  "AUDCHF": "25,12", // AUD/CHF - Australia, Switzerland
  "AUDCAD": "25,6", // AUD/CAD - Australia, Canada
  "AUDNZD": "25,43", // AUD/NZD - Australia, New Zealand
  "CADJPY": "6,35", // CAD/JPY - Canada, Japan
  "CADCHF": "6,12", // CAD/CHF - Canada, Switzerland
  "CHFJPY": "12,35", // CHF/JPY - Switzerland, Japan
  "NZDJPY": "43,35", // NZD/JPY - New Zealand, Japan
  "NZDCHF": "43,12", // NZD/CHF - New Zealand, Switzerland
  "NZDCAD": "43,6", // NZD/CAD - New Zealand, Canada

  // Exotic Pairs
  "USDSGD": "5,36", // USD/SGD - United States, Singapore
  "USDHKD": "5,39", // USD/HKD - United States, Hong Kong
  "USDNOK": "5,60", // USD/NOK - United States, Norway
  "USDSEK": "5,9", // USD/SEK - United States, Sweden
  "USDDKK": "5,24", // USD/DKK - United States, Denmark
  "USDPLN": "5,53", // USD/PLN - United States, Poland
  "USDCZK": "5,22", // USD/CZK - United States, Czech Republic
  "USDHUF": "5,33", // USD/HUF - United States, Hungary
  "USDTRY": "5,7", // USD/TRY - United States, Turkey
  "USDZAR": "5,17", // USD/ZAR - United States, South Africa
  "USDMXN": "5,34", // USD/MXN - United States, Mexico

  // Commodities
  "XAGUSD": "5", // Silver/USD - United States
  "XPTUSD": "5", // Platinum/USD - United States
  "XPDUSD": "5", // Palladium/USD - United States

  // Oil
  "USOIL": "5", // WTI Crude Oil - United States
  "UKOIL": "5,4", // Brent Crude Oil - United States, United Kingdom

  // Indices (showing major economic regions)
  "US30": "5", // Dow Jones - United States
  "US500": "5", // S&P 500 - United States
  "NAS100": "5", // NASDAQ - United States
  "UK100": "4", // FTSE 100 - United Kingdom
  "GER30": "56", // DAX - Germany
  "FRA40": "8", // CAC 40 - France
  "JPN225": "35", // Nikkei - Japan
  "AUS200": "25", // ASX 200 - Australia
  "HK50": "39", // Hang Seng - Hong Kong

  // Crypto (US regulation focus)
  "BTCUSD": "5", // Bitcoin/USD - United States
  "ETHUSD": "5", // Ethereum/USD - United States
  "LTCUSD": "5", // Litecoin/USD - United States
  "XRPUSD": "5", // Ripple/USD - United States
};

export const SYMBOL_OPTIONS = [
  // Top priority pairs (XU and GU first as requested)
  { label: "XAUUSD", value: "XAUUSD", countries: SYMBOL_COUNTRY_MAP["XAUUSD"], point: 0.01 },
  { label: "GBPUSD", value: "GBPUSD", countries: SYMBOL_COUNTRY_MAP["GBPUSD"], point: 0.00001 },

  // Major Forex Pairs
  { label: "EURUSD", value: "EURUSD", countries: SYMBOL_COUNTRY_MAP["EURUSD"], point: 0.00001 },
  { label: "USDJPY", value: "USDJPY", countries: SYMBOL_COUNTRY_MAP["USDJPY"], point: 0.001 },
  { label: "USDCHF", value: "USDCHF", countries: SYMBOL_COUNTRY_MAP["USDCHF"], point: 0.00001 },
  { label: "AUDUSD", value: "AUDUSD", countries: SYMBOL_COUNTRY_MAP["AUDUSD"], point: 0.00001 },
  { label: "NZDUSD", value: "NZDUSD", countries: SYMBOL_COUNTRY_MAP["NZDUSD"], point: 0.00001 },
  { label: "USDCAD", value: "USDCAD", countries: SYMBOL_COUNTRY_MAP["USDCAD"], point: 0.00001 },

  // Cross Pairs
  { label: "EURGBP", value: "EURGBP", countries: SYMBOL_COUNTRY_MAP["EURGBP"], point: 0.00001 },
  { label: "EURJPY", value: "EURJPY", countries: SYMBOL_COUNTRY_MAP["EURJPY"], point: 0.001 },
  { label: "EURCHF", value: "EURCHF", countries: SYMBOL_COUNTRY_MAP["EURCHF"], point: 0.00001 },
  { label: "EURAUD", value: "EURAUD", countries: SYMBOL_COUNTRY_MAP["EURAUD"], point: 0.00001 },
  { label: "EURCAD", value: "EURCAD", countries: SYMBOL_COUNTRY_MAP["EURCAD"], point: 0.00001 },
  { label: "EURNZD", value: "EURNZD", countries: SYMBOL_COUNTRY_MAP["EURNZD"], point: 0.00001 },
  { label: "GBPJPY", value: "GBPJPY", countries: SYMBOL_COUNTRY_MAP["GBPJPY"], point: 0.001 },
  { label: "GBPCHF", value: "GBPCHF", countries: SYMBOL_COUNTRY_MAP["GBPCHF"], point: 0.00001 },
  { label: "GBPAUD", value: "GBPAUD", countries: SYMBOL_COUNTRY_MAP["GBPAUD"], point: 0.00001 },
  { label: "GBPCAD", value: "GBPCAD", countries: SYMBOL_COUNTRY_MAP["GBPCAD"], point: 0.00001 },
  { label: "GBPNZD", value: "GBPNZD", countries: SYMBOL_COUNTRY_MAP["GBPNZD"], point: 0.00001 },
  { label: "AUDJPY", value: "AUDJPY", countries: SYMBOL_COUNTRY_MAP["AUDJPY"], point: 0.001 },
  { label: "AUDCHF", value: "AUDCHF", countries: SYMBOL_COUNTRY_MAP["AUDCHF"], point: 0.00001 },
  { label: "AUDCAD", value: "AUDCAD", countries: SYMBOL_COUNTRY_MAP["AUDCAD"], point: 0.00001 },
  { label: "AUDNZD", value: "AUDNZD", countries: SYMBOL_COUNTRY_MAP["AUDNZD"], point: 0.00001 },
  { label: "CADJPY", value: "CADJPY", countries: SYMBOL_COUNTRY_MAP["CADJPY"], point: 0.001 },
  { label: "CADCHF", value: "CADCHF", countries: SYMBOL_COUNTRY_MAP["CADCHF"], point: 0.00001 },
  { label: "CHFJPY", value: "CHFJPY", countries: SYMBOL_COUNTRY_MAP["CHFJPY"], point: 0.001 },
  { label: "NZDJPY", value: "NZDJPY", countries: SYMBOL_COUNTRY_MAP["NZDJPY"], point: 0.001 },
  { label: "NZDCHF", value: "NZDCHF", countries: SYMBOL_COUNTRY_MAP["NZDCHF"], point: 0.00001 },
  { label: "NZDCAD", value: "NZDCAD", countries: SYMBOL_COUNTRY_MAP["NZDCAD"], point: 0.00001 },

  // Exotic Pairs
  { label: "USDSGD", value: "USDSGD", countries: SYMBOL_COUNTRY_MAP["USDSGD"], point: 0.00001 },
  { label: "USDHKD", value: "USDHKD", countries: SYMBOL_COUNTRY_MAP["USDHKD"], point: 0.00001 },
  { label: "USDNOK", value: "USDNOK", countries: SYMBOL_COUNTRY_MAP["USDNOK"], point: 0.00001 },
  { label: "USDSEK", value: "USDSEK", countries: SYMBOL_COUNTRY_MAP["USDSEK"], point: 0.00001 },
  { label: "USDDKK", value: "USDDKK", countries: SYMBOL_COUNTRY_MAP["USDDKK"], point: 0.00001 },
  { label: "USDPLN", value: "USDPLN", countries: SYMBOL_COUNTRY_MAP["USDPLN"], point: 0.00001 },
  { label: "USDCZK", value: "USDCZK", countries: SYMBOL_COUNTRY_MAP["USDCZK"], point: 0.00001 },
  { label: "USDHUF", value: "USDHUF", countries: SYMBOL_COUNTRY_MAP["USDHUF"], point: 0.001 },
  { label: "USDTRY", value: "USDTRY", countries: SYMBOL_COUNTRY_MAP["USDTRY"], point: 0.00001 },
  { label: "USDZAR", value: "USDZAR", countries: SYMBOL_COUNTRY_MAP["USDZAR"], point: 0.00001 },
  { label: "USDMXN", value: "USDMXN", countries: SYMBOL_COUNTRY_MAP["USDMXN"], point: 0.00001 },

  // Commodities
  { label: "XAGUSD", value: "XAGUSD", countries: SYMBOL_COUNTRY_MAP["XAGUSD"], point: 0.001 },
  { label: "XPTUSD", value: "XPTUSD", countries: SYMBOL_COUNTRY_MAP["XPTUSD"], point: 0.01 },
  { label: "XPDUSD", value: "XPDUSD", countries: SYMBOL_COUNTRY_MAP["XPDUSD"], point: 0.01 },

  // Oil
  { label: "USOIL", value: "USOIL", countries: SYMBOL_COUNTRY_MAP["USOIL"], point: 0.01 },
  { label: "UKOIL", value: "UKOIL", countries: SYMBOL_COUNTRY_MAP["UKOIL"], point: 0.01 },

  // Indices
  { label: "US30", value: "US30", countries: SYMBOL_COUNTRY_MAP["US30"], point: 1 },
  { label: "US500", value: "US500", countries: SYMBOL_COUNTRY_MAP["US500"], point: 0.1 },
  { label: "NAS100", value: "NAS100", countries: SYMBOL_COUNTRY_MAP["NAS100"], point: 0.1 },
  { label: "UK100", value: "UK100", countries: SYMBOL_COUNTRY_MAP["UK100"], point: 0.1 },
  { label: "GER30", value: "GER30", countries: SYMBOL_COUNTRY_MAP["GER30"], point: 0.1 },
  { label: "FRA40", value: "FRA40", countries: SYMBOL_COUNTRY_MAP["FRA40"], point: 0.1 },
  { label: "JPN225", value: "JPN225", countries: SYMBOL_COUNTRY_MAP["JPN225"], point: 1 },
  { label: "AUS200", value: "AUS200", countries: SYMBOL_COUNTRY_MAP["AUS200"], point: 0.1 },
  { label: "HK50", value: "HK50", countries: SYMBOL_COUNTRY_MAP["HK50"], point: 1 },

  // Crypto
  { label: "BTCUSD", value: "BTCUSD", countries: SYMBOL_COUNTRY_MAP["BTCUSD"], point: 0.01 },
  { label: "ETHUSD", value: "ETHUSD", countries: SYMBOL_COUNTRY_MAP["ETHUSD"], point: 0.01 },
  { label: "LTCUSD", value: "LTCUSD", countries: SYMBOL_COUNTRY_MAP["LTCUSD"], point: 0.01 },
  { label: "XRPUSD", value: "XRPUSD", countries: SYMBOL_COUNTRY_MAP["XRPUSD"], point: 0.00001 },
];

// Smart Lot Distribution Configuration
export const LOT_DISTRIBUTION_CONFIG = {
  maxLotPerMessage: 0.1,
  lotDistribution: {
    1: [100],
    2: [70, 30],
    3: [70, 20, 10],
    4: [65, 20, 10, 5],
    5: [50, 25, 15, 9, 1]
  } as const,
  minLotSize: 0.01,
  description: "Lot distribution configuration for trading signals. Percentages should add up to 100 for each TP count."
};

// Valid trading actions that our system accepts
export const VALID_ACTIONS = [
  "Buy Limit",
  "Sell Limit",
  "Buy Now",
  "Sell Now"
] as const;

// Action mapping for intelligent signal parsing
export const ACTION_MAPPING: { [key: string]: string } = {
  // Exact matches (case-insensitive)
  "buy limit": "Buy Limit",
  "sell limit": "Sell Limit",
  "buy now": "Buy Now",
  "sell now": "Sell Now",

  // Common variations that should map to exact matches
  "buylimit": "Buy Limit",
  "selllimit": "Sell Limit",
  "buynow": "Buy Now",
  "sellnow": "Sell Now",

  // Generic buy/sell actions that map to Limit orders (safer default)
  "buy": "Buy Limit",
  "sell": "Sell Limit",
  "follow buy": "Buy Limit",
  "follow sell": "Sell Limit",
  "followbuy": "Buy Limit",
  "followsell": "Sell Limit",

  // Market orders
  "market buy": "Buy Now",
  "market sell": "Sell Now",
  "instant buy": "Buy Now",
  "instant sell": "Sell Now",

  // Long/Short terminology
  "long": "Buy Limit",
  "short": "Sell Limit",
  "go long": "Buy Limit",
  "go short": "Sell Limit",
};

// Timeframe options for chart data
export const TIMEFRAME_OPTIONS = [
  { value: "M1", label: "1 Minute" },
  { value: "M5", label: "5 Minutes" },
  { value: "M15", label: "15 Minutes" },
  { value: "M30", label: "30 Minutes" },
  { value: "H1", label: "1 Hour" },
  { value: "H4", label: "4 Hours" },
  { value: "D1", label: "Daily" },
  { value: "W1", label: "Weekly" },
  { value: "MN1", label: "Monthly" },
];

// AI Provider options
export const AI_PROVIDER_OPTIONS = [
  { value: "gpt", label: "GPT (OpenAI)" },
  { value: "gemini", label: "Gemini (Google)" },
];

// Group Configuration for easier management
//
// HOW TO ADD NEW GROUPS:
// Simply add a new object to the GROUP_CONFIGS array below. No need to modify any other files!
//
// For filtered groups (like WH): Set requiresGroupId: false and provide filterFunction
// For grouped data (like ZD/IN): Set requiresGroupId: true and no filterFunction needed
//
// Examples:
// - Filter by symbol: item.symbol === 'XAUUSD'
// - Filter by comment prefix: item.comment && item.comment.startsWith('ABC')
// - Filter by profit: item.profit && item.profit > 0
// - Filter by order type: item.type === 'BUY'
//
export interface GroupConfig {
  id: string;           // Unique identifier (used in URLs and internal logic)
  name: string;         // Internal name (usually same as id)
  displayName: string;  // Display name shown in UI tabs
  description: string;  // Description for documentation
  requiresGroupId: boolean; // true for ZD/IN style groups, false for filtered groups
  filterFunction?: (item: any) => boolean; // Filter function for non-grouped data
}

export const GROUP_CONFIGS: GroupConfig[] = [
  {
    id: "orders",
    name: "orders",
    displayName: "Orders",
    description: "All orders and positions",
    requiresGroupId: false
  },
  {
    id: "zd",
    name: "zd",
    displayName: "ZD Groups",
    description: "ZD grouped orders",
    requiresGroupId: true
  },
  {
    id: "in",
    name: "in",
    displayName: "IN Groups",
    description: "IN grouped orders",
    requiresGroupId: true
  },
  {
    id: "wh",
    name: "wh",
    displayName: "WH",
    description: "All orders starting with WH",
    requiresGroupId: false,
    filterFunction: (item: any) => {
      return item.comment && item.comment.toUpperCase().startsWith('WH');
    }
  },
  // {
  //   id: "sg",
  //   name: "sg",
  //   displayName: "SG",
  //   description: "All orders starting with SG",
  //   requiresGroupId: false,
  //   filterFunction: (item: any) => {
  //     return item.comment && item.comment.toUpperCase().startsWith('SG');
  //   }
  // },
  // Example: Add more groups easily - just uncomment and modify as needed
  // {
  //   id: "gold",
  //   name: "gold",
  //   displayName: "Gold",
  //   description: "All XAUUSD orders",
  //   requiresGroupId: false,
  //   filterFunction: (item: any) => {
  //     return item.symbol === 'XAUUSD';
  //   }
  // },
  // {
  //   id: "profit",
  //   name: "profit",
  //   displayName: "Profitable",
  //   description: "All profitable positions",
  //   requiresGroupId: false,
  //   filterFunction: (item: any) => {
  //     return item.profit && item.profit > 0;
  //   }
  // }
];

// Helper function to get group config by id
export function getGroupConfig(groupId: string): GroupConfig | undefined {
  return GROUP_CONFIGS.find(config => config.id === groupId);
}

// Helper function to get symbol point data
export function getSymbolPoint(symbol: string): number {
  const cleanSymbol = symbol.split('.')[0];
  const symbolOption = SYMBOL_OPTIONS.find(option => option.value === cleanSymbol);
  return symbolOption?.point || 0.00001; // Default to 0.00001 for forex pairs
}

// Utility functions for formatting numbers
export const formatNumber = (n: number, d: number = 2) => {
  var f = (!d) ? 1 : d;
  var r = (n).toFixed(f).replace(/\d(?=(\d{3})+\.)/g, '$&,');
  if (d)
      return r;
  return r.split('.')[0];
};

// Format number based on symbol point configuration
export const formatNumberBySymbol = (num: number, symbol: string) => {
  // Clean symbol by removing suffixes like .s, .iux, .m, etc.
  // const cleanSymbol = symbol.split('.')[0];
  const symbolPoint = getSymbolPoint(symbol);
  const decimals = symbolPoint >= 1 ? 0 : symbolPoint >= 0.01 ? 2 : symbolPoint >= 0.001 ? 3 : 5;
  return num.toFixed(decimals);
};

// Calculate Risk-Reward ratio
export const calculateRiskReward = (entryPrice: number, slPrice: number, tpPrice: number, orderType: string): string => {
  if (entryPrice === 0 || slPrice === 0 || tpPrice === 0) return "-";

  const isBuyAction = orderType.toLowerCase().includes('buy');

  let risk: number;
  let reward: number;

  if (isBuyAction) {
    risk = Math.abs(entryPrice - slPrice);
    reward = Math.abs(tpPrice - entryPrice);
  } else {
    risk = Math.abs(slPrice - entryPrice);
    reward = Math.abs(entryPrice - tpPrice);
  }

  if (risk === 0) return "-";

  const ratio = reward / risk;
  return `1 : ${ratio.toFixed(1)}`;
};

// Function to intelligently map action strings to valid actions
export function mapActionToValid(action: string): string {
  if (!action) return "Buy Limit"; // Default fallback

  const cleanAction = action.trim().toLowerCase();

  // Check for exact match first
  if (ACTION_MAPPING[cleanAction]) {
    return ACTION_MAPPING[cleanAction];
  }

  // Check if it contains buy or sell keywords
  if (cleanAction.includes('buy')) {
    return "Buy Limit";
  } else if (cleanAction.includes('sell')) {
    return "Sell Limit";
  }

  // Default fallback
  return "Buy Limit";
}
