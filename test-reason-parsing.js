// Test script to verify Reason field parsing functionality
// This can be run in the browser console to test the parsing

const testLongText = `Signal ID: test123
C.GPT
Symbol: XAUUSD
Signal: Buy Limit
Price: 2650.00
SL: 2640.00
TP1: 2660.00
TP2: 2670.00
TP3: 2680.00
Reason: H4 uptrend above EMA20; H1 pullback into overlapping demand 3840-3852 with 61.8% Fib and pivot support. RSI midline bounce, MACD improving. Enter mid-zone for EMA retest confluence.`;

console.log('Testing Reason field parsing...');
console.log('Sample text:', testLongText);

// Test the parsing logic
const lines = testLongText.split("\n").map((line) => line.trim());
let parsedReason = "";

lines.forEach((line) => {
  const reasonMatch = line.match(/Reason\s*:\s*(.+)/i);
  if (reasonMatch) {
    parsedReason = reasonMatch[1].trim();
    console.log('Found Reason:', parsedReason);
  }
});

if (parsedReason) {
  console.log('✅ Reason parsing successful!');
  console.log('Parsed Reason:', parsedReason);
} else {
  console.log('❌ Reason parsing failed!');
}

// Test with different formats
const testCases = [
  'Reason: Simple reason text',
  'reason: lowercase reason',
  'REASON: uppercase reason',
  'Reason:No space after colon',
  'Reason:   Multiple spaces after colon',
];

console.log('\nTesting different Reason formats:');
testCases.forEach((testCase, index) => {
  const match = testCase.match(/Reason\s*:\s*(.+)/i);
  if (match) {
    console.log(`✅ Test ${index + 1}: "${testCase}" -> "${match[1].trim()}"`);
  } else {
    console.log(`❌ Test ${index + 1}: "${testCase}" -> No match`);
  }
});
